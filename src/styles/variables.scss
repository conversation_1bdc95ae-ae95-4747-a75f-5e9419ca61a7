// ============== 全局颜色变量 ==============

// 主题色系
$themeColor: #3de9fa;                    // 主题蓝色
$themeColorSecondary: #1196FC;           // 次要主题色
$whiteColor: #fff;                       // 纯白色
$borderColor: #000;                      // 边框色

// 状态颜色
$successColor: #27edbb;                  // 成功/在线状态
$warningColor: #e3731b;                  // 警告状态
$dangerColor: #ff4757;                   // 危险/错误状态
$infoColor: #3de9fa;                     // 信息状态
$offlineColor: #b8bcbf;                  // 离线状态

// 文本颜色
$textPrimary: #E6F4FF;                   // 主要文本
$textSecondary: #c7dfff;                 // 次要文本
$textTertiary: #829CBD;                  // 三级文本
$textPlaceholder: #7A9BBD;               // 占位符文本
$textDisabled: #6a8fbd;                  // 禁用文本

// 背景颜色 - 毛玻璃效果基础色
$bgPrimary: rgba(16, 52, 87, 0.6);       // 主要背景
$bgSecondary: rgba(24, 64, 104, 0.4);    // 次要背景
$bgTertiary: rgba(6, 30, 54, 0.9);       // 三级背景
$bgQuaternary: rgba(12, 45, 78, 0.8);    // 四级背景
$bgOverlay: rgba(9, 24, 34, 0.95);       // 遮罩背景

// 边框颜色
$borderPrimary: rgba(61, 233, 250, 0.3); // 主要边框
$borderSecondary: rgba(61, 233, 250, 0.2); // 次要边框
$borderHover: rgba(61, 233, 250, 0.5);   // 悬停边框
$borderActive: rgba(61, 233, 250, 0.6);  // 激活边框

// 按钮颜色
$addBtnBgColor: rgba(61, 233, 250, 0.5); // 添加按钮背景
$delBtnBorderColor: #b4061a;             // 删除按钮边框
$delBtnBgColor: rgba(180, 6, 26, 0.5);   // 删除按钮背景
$amendBtnBorderColor: #E3731B;           // 修改按钮边框
$amendBtnBgColor: rgba(227, 115, 27, 0.5); // 修改按钮背景

// 阴影颜色
$shadowPrimary: rgba(0, 0, 0, 0.4);      // 主要阴影
$shadowSecondary: rgba(0, 0, 0, 0.2);    // 次要阴影
$shadowGlow: rgba(61, 233, 250, 0.2);    // 发光阴影
$shadowInset: rgba(255, 255, 255, 0.1);  // 内阴影

// 渐变色定义
$gradientPrimary: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);
$gradientSecondary: linear-gradient(135deg, $bgTertiary 0%, $bgQuaternary 100%);
$gradientTheme: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%);
$gradientSuccess: linear-gradient(135deg, rgba(39, 237, 187, 0.8) 0%, rgba(16, 185, 129, 0.6) 100%);
$gradientWarning: linear-gradient(135deg, rgba(227, 115, 27, 0.8) 0%, rgba(245, 158, 11, 0.6) 100%);
$gradientDanger: linear-gradient(135deg, rgba(255, 71, 87, 0.8) 0%, rgba(239, 68, 68, 0.6) 100%);