@use './variables.scss' as *;
@use './mixin.scss' as *;

// ============== 全局组件样式 ==============

// 左右面板基础样式 - 统一的 .left 和 .right 面板
.panel-base {
  @include glassEffect(0.6);
  margin-top: 70px;
  height: calc(100% - 100px);
  z-index: 100;
  position: absolute;
  background-image: url("/src/assets/images/dialog/panel_bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 20px;
  animation-duration: 0.6s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;

  .header {
    @include panelHeader;
  }

  .content {
    height: calc(100% - 60px);
    overflow-y: auto;
    @include scrollBar;
  }

  .list-item {
    @include listItem;
    font-size: 14px;
    font-family: "Alibaba-PuHuiTi", "PingFang SC";
    color: $textSecondary;

    &.active {
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
      border-left: 3px solid $themeColor;
      color: $themeColor;
      font-weight: 500;
    }

    &.online {
      .status-dot {
        background: $successColor;
        box-shadow: 0 0 6px rgba(39, 237, 187, 0.4);
      }
    }

    &.offline {
      .status-dot {
        background: $offlineColor;
      }
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
      transition: all 0.3s ease;
    }
  }
}

// 左面板特定样式
.left {
  @extend .panel-base;
  left: 15px;
  width: 387px;
  animation-name: slideInLeft;

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

// 右面板特定样式
.right {
  @extend .panel-base;
  right: 15px;
  width: 387px;
  animation-name: slideInRight;

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

// 统计卡片样式
.stat-card {
  @include glassEffect(0.4);
  padding: 16px;
  text-align: center;
  @include hoverEffect;

  .stat-number {
    font-size: 32px;
    font-family: "BEBAS", "Arial";
    font-weight: 400;
    color: $textPrimary;
    background: linear-gradient(0deg, #FFFFFF 0%, $themeColorSecondary 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px $shadowPrimary;
  }

  .stat-label {
    font-size: 14px;
    font-family: "Alibaba-PuHuiTi", "PingFang SC";
    color: $textSecondary;
    text-shadow: 0 1px 2px $shadowPrimary;
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
  }
}

// 操作按钮组样式
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;

  .btn {
    @include buttonBase;
    min-width: 60px;
    padding: 6px 12px;
    font-size: 12px;

    &.primary {
      background: $gradientTheme;
      border-color: $themeColor;
      color: $whiteColor;

      &:hover {
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%);
        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4);
      }
    }

    &.success {
      background: $gradientSuccess;
      border-color: $successColor;
      color: $whiteColor;

      &:hover {
        background: linear-gradient(135deg, rgba(39, 237, 187, 0.9) 0%, rgba(16, 185, 129, 0.7) 100%);
        box-shadow: 0 0 12px rgba(39, 237, 187, 0.4);
      }
    }

    &.warning {
      background: $gradientWarning;
      border-color: $warningColor;
      color: $whiteColor;

      &:hover {
        background: linear-gradient(135deg, rgba(227, 115, 27, 0.9) 0%, rgba(245, 158, 11, 0.7) 100%);
        box-shadow: 0 0 12px rgba(227, 115, 27, 0.4);
      }
    }

    &.danger {
      background: $gradientDanger;
      border-color: $dangerColor;
      color: $whiteColor;

      &:hover {
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.9) 0%, rgba(239, 68, 68, 0.7) 100%);
        box-shadow: 0 0 12px rgba(255, 71, 87, 0.4);
      }
    }
  }
}

// 搜索框样式
.search-box {
  @include glassEffect(0.4);
  padding: 8px 12px;
  margin-bottom: 12px;

  .el-input {
    .el-input__wrapper {
      @include inputBase;
    }
  }
}

// 标签页样式
.tab-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .tab-item {
    @include buttonBase;
    padding: 8px 16px;
    margin-right: 8px;
    background-image: url("./img/tab.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    min-width: 104px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-image: url("./img/tab_active.png");
      color: $themeColor;
      background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .name {
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi", "PingFang SC";
      font-weight: 400;
    }
  }
}

// 状态指示器样式
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px $shadowPrimary;

  &.online {
    background: linear-gradient(135deg, rgba(39, 237, 187, 0.3) 0%, rgba(39, 237, 187, 0.1) 100%);
    color: $successColor;
    border: 1px solid rgba(39, 237, 187, 0.4);
  }

  &.offline {
    background: linear-gradient(135deg, rgba(184, 188, 191, 0.3) 0%, rgba(184, 188, 191, 0.1) 100%);
    color: $offlineColor;
    border: 1px solid rgba(184, 188, 191, 0.4);
  }

  &.warning {
    background: linear-gradient(135deg, rgba(227, 115, 27, 0.3) 0%, rgba(227, 115, 27, 0.1) 100%);
    color: $warningColor;
    border: 1px solid rgba(227, 115, 27, 0.4);
  }

  &.error {
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.3) 0%, rgba(255, 71, 87, 0.1) 100%);
    color: $dangerColor;
    border: 1px solid rgba(255, 71, 87, 0.4);
  }

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    box-shadow: 0 0 4px currentColor;
  }
}

// 进度条样式
.progress-bar {
  width: 100%;
  height: 6px;
  background: $bgSecondary;
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid $borderSecondary;

  .progress-fill {
    height: 100%;
    background: $gradientTheme;
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 8px rgba(61, 233, 250, 0.3);
  }
}
