@use './variables.scss' as *;

// ============== 全局工具类样式 ==============

// 布局工具类
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.h_center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 尺寸工具类
.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.full-size {
  width: 100%;
  height: 100%;
}

// 间距工具类
.ml {
  margin-left: 10px !important;
}

.p1 {
  padding: 15px;
}

.no-margin {
  margin: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

// 文本工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-nowrap {
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-all;
  word-wrap: break-word;
}

// 颜色工具类
.text-primary {
  color: $textPrimary;
}

.text-secondary {
  color: $textSecondary;
}

.text-theme {
  color: $themeColor;
}

.text-success {
  color: $successColor;
}

.text-warning {
  color: $warningColor;
}

.text-danger {
  color: $dangerColor;
}

.text-disabled {
  color: $textDisabled;
}

// 背景工具类
.bg-primary {
  background: $gradientPrimary;
}

.bg-secondary {
  background: $gradientSecondary;
}

.bg-theme {
  background: $gradientTheme;
}

.bg-success {
  background: $gradientSuccess;
}

.bg-warning {
  background: $gradientWarning;
}

.bg-danger {
  background: $gradientDanger;
}

.bg-transparent {
  background: transparent;
}

// 边框工具类
.border-primary {
  border: 1px solid $borderPrimary;
}

.border-secondary {
  border: 1px solid $borderSecondary;
}

.border-theme {
  border: 1px solid $themeColor;
}

.border-none {
  border: none;
}

.border-radius {
  border-radius: 8px;
}

.border-radius-sm {
  border-radius: 4px;
}

.border-radius-lg {
  border-radius: 12px;
}

// 阴影工具类
.shadow-primary {
  box-shadow: 0 4px 16px $shadowPrimary;
}

.shadow-secondary {
  box-shadow: 0 2px 8px $shadowSecondary;
}

.shadow-glow {
  box-shadow: 0 0 12px $shadowGlow;
}

.shadow-none {
  box-shadow: none;
}

// 位置工具类
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

// 定位工具类
.left-top {
  position: absolute;
  top: 0;
  left: 0;
}

.right-top {
  position: absolute;
  top: 0;
  right: 0;
}

.left-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
}

.right-bottom {
  position: absolute;
  bottom: 0;
  right: 0;
}

// 显示隐藏工具类
.hidden {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

// 鼠标样式工具类
.cursor {
  cursor: pointer;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// 溢出处理工具类
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

// Z-index工具类
.z-index-1 {
  z-index: 1;
}

.z-index-10 {
  z-index: 10;
}

.z-index-100 {
  z-index: 100;
}

.z-index-1000 {
  z-index: 1000;
}

// 清除浮动工具类
.clearfix::after {
  content: "";
  display: block;
  height: 0px;
  clear: both;
}

// 动画工具类
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.6s ease;
}

.no-transition {
  transition: none;
}

// 变换工具类
.transform-none {
  transform: none;
}

.scale-hover:hover {
  transform: scale(1.05);
}

.translate-y-hover:hover {
  transform: translateY(-2px);
}

// 毛玻璃效果工具类
.glass-light {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.glass-medium {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.glass-heavy {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

// 渐变文字工具类
.gradient-text-theme {
  background: linear-gradient(135deg, $themeColor 0%, $themeColorSecondary 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text-primary {
  background: linear-gradient(135deg, $textPrimary 0%, $themeColorSecondary 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

// 响应式工具类
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full-width {
    width: 100%;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none;
  }
}
