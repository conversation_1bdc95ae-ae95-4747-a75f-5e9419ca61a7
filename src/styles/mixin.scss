@use './variables.scss' as *;

// ============== 全局 Mixin 混入 ==============

// 清除浮动
@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

// 自定义滚动条 - 与主题色协调
@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: $bgSecondary;
    border-radius: 4px;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: $gradientTheme;
    border-radius: 4px;
    border: 1px solid $borderPrimary;

    &:hover {
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%);
    }
  }

  &::-webkit-scrollbar-corner {
    background: $bgSecondary;
  }
}

// 相对定位容器
@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

// 毛玻璃效果 - 统一的毛玻璃背景
@mixin glassEffect($opacity: 0.6) {
  background: linear-gradient(135deg, rgba(16, 52, 87, $opacity) 0%, rgba(24, 64, 104, $opacity * 0.7) 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid $borderPrimary;
  border-radius: 12px;
  box-shadow: 0 8px 32px $shadowPrimary, inset 0 1px 0 $shadowInset;
}

// 悬停效果
@mixin hoverEffect {
  transition: all 0.3s ease;

  &:hover {
    border-color: $borderHover;
    box-shadow: 0 0 12px $shadowGlow, inset 0 1px 0 $shadowInset;
    transform: translateY(-2px);
  }
}

// 面板标题样式
@mixin panelHeader {
  font-size: 18px;
  font-family: "DOUYU", "Alibaba-PuHuiTi", "PingFang SC";
  font-weight: 500;
  color: $textPrimary;
  text-shadow: 0 2px 4px $shadowPrimary;
  padding: 12px 8px;
  border-bottom: 1px solid $borderSecondary;
  margin-bottom: 15px;

  img {
    filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
  }
}

// 列表项样式
@mixin listItem {
  background: $gradientPrimary;
  border: 1px solid $borderSecondary;
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(6px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: $borderHover;
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }
  }
}

// 按钮基础样式
@mixin buttonBase {
  background: $gradientPrimary;
  border: 1px solid $borderPrimary;
  border-radius: 6px;
  color: $textPrimary;
  font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
  font-weight: 500;
  text-shadow: 0 1px 2px $shadowPrimary;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 0 $shadowInset;
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
    border-color: $borderHover;
    box-shadow: 0 0 8px $shadowGlow, inset 0 1px 0 $shadowInset;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px $shadowSecondary;
  }

  &:disabled {
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
    border-color: rgba(61, 233, 250, 0.1);
    color: $textDisabled;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
  }
}

// 输入框基础样式
@mixin inputBase {
  background: $gradientPrimary;
  border: 1px solid $borderPrimary;
  border-radius: 8px;
  box-shadow: inset 0 2px 4px $shadowSecondary;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  color: $textPrimary;
  font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
  font-size: 14px;

  &:hover {
    border-color: $borderHover;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
    box-shadow: 0 0 8px $shadowGlow, inset 0 2px 4px $shadowSecondary;
  }

  &:focus {
    border-color: $themeColor;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
    box-shadow: 0 0 12px rgba(61, 233, 250, 0.4), inset 0 2px 4px $shadowSecondary;
    outline: none;
  }

  &::placeholder {
    color: $textPlaceholder;
  }

  &:disabled {
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
    border-color: rgba(61, 233, 250, 0.1);
    color: $textDisabled;
    cursor: not-allowed;
    opacity: 0.6;
  }
}
