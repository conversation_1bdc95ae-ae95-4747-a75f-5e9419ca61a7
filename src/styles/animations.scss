@use './variables.scss' as *;

// ============== 全局动画样式 ==============

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0 !important;
}

// 左侧面板滑入动画
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 右侧面板滑入动画
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 从上方滑入动画
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 从下方滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 淡入向上动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 统计卡片淡入动画
@keyframes statCardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 数字脉冲动画
@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 图标旋转动画
@keyframes iconRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 波纹效果动画
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

// 旋转动画（用于加载等）
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 呼吸灯效果
@keyframes breathe {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 闪烁动画
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// 弹跳动画
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-15px);
  }
  70% {
    transform: translateY(-7px);
  }
  90% {
    transform: translateY(-3px);
  }
}

// 摇摆动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

// 缩放进入动画
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  to {
    transform: scale(1);
  }
}

// 缩放退出动画
@keyframes zoomOut {
  from {
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(0.3);
  }
  to {
    opacity: 0;
    transform: scale(0.3);
  }
}

// 动画工具类
.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.5s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-statCardFadeIn {
  animation: statCardFadeIn 0.8s ease-out;
}

.animate-numberPulse {
  animation: numberPulse 2s ease-in-out infinite;
}

.animate-iconRotate {
  animation: iconRotate 2s linear infinite;
}

.animate-ripple {
  animation: ripple 0.6s linear;
}

.animate-rotate {
  animation: rotate 2s linear infinite;
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
}

.animate-blink {
  animation: blink 1s linear infinite;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-zoomIn {
  animation: zoomIn 0.5s ease-out;
}

.animate-zoomOut {
  animation: zoomOut 0.5s ease-out;
}

// 动画延迟工具类
.animate-delay-1 {
  animation-delay: 0.1s;
}

.animate-delay-2 {
  animation-delay: 0.2s;
}

.animate-delay-3 {
  animation-delay: 0.3s;
}

.animate-delay-4 {
  animation-delay: 0.4s;
}

.animate-delay-5 {
  animation-delay: 0.5s;
}

// 动画持续时间工具类
.animate-duration-fast {
  animation-duration: 0.3s;
}

.animate-duration-normal {
  animation-duration: 0.6s;
}

.animate-duration-slow {
  animation-duration: 1s;
}

// 动画填充模式工具类
.animate-fill-both {
  animation-fill-mode: both;
}

.animate-fill-forwards {
  animation-fill-mode: forwards;
}

.animate-fill-backwards {
  animation-fill-mode: backwards;
}

// 动画播放状态工具类
.animate-paused {
  animation-play-state: paused;
}

.animate-running {
  animation-play-state: running;
}
