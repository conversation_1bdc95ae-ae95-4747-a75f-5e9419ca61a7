<template>
    <div class="layout-header">
        <div class="header_left">
            <div v-if="weatherPng" @click="clickWeather" class="cursor">
                <img :src="weatherPng" />
            </div>
            <div class="temperature" v-if="weatherPng">
                <div><span class="num">{{ weather }} {{ temperature }}</span>℃</div>
                <div>
                    {{ date }}
                </div>
            </div>

            <div class=" mode">

            </div>
        </div>
        <div class="logo">
            <div class="center_container" @click="goHome">
                <div class="title">{{ projectName || '德福科技研发大楼' }}</div>
            </div>
        </div>
        <div class="header_right">


            <el-badge @click="showAlert" @mouseup.stop :value="alarmTotal" class="alarm-icon icon" :max="99"
                type="danger" :class="{ ld: alarmTotal != 0 }">
                <img src="@/assets/images/head/icon_02c.png" />
            </el-badge>


            <div class="icon cursor" @click="clickRoad">
                <img class="icon" src="@/assets/images/head/icon_05c.png" />
            </div>

            <el-dropdown trigger="click" @command="userCommand">
                <span class="el-dropdown-link">
                    <span class="user-name">{{ userName }}</span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="personal">个人中心</el-dropdown-item>
                        <el-dropdown-item command="system">系统配置</el-dropdown-item>
                        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>

        <teleport to="body">
            <Transition name="fade" mode="out-in">
                <alarm-list id="treeWrap" v-if="dialogVisible" :total="alarmTotal"
                    @current-change="handleCurrentChange" />
            </Transition>
        </teleport>

        <alarm-dialog :dialogData="dialogData"></alarm-dialog>
        <audio hidden="true" loop>
            <source src="/src/assets/images/woop.mp3" type="audio/mpeg" />
        </audio>

        <Transition name="fade" mode="out-in" appear>
            <div class="weather" v-if="showWeather">
                <Weather @clickWeather="clickWeather"></Weather>
            </div>
        </Transition>

        <Transition name="fade" mode="out-in" appear>
            <div class="road" v-if="showRoad">
                <road @clickRoad="clickRoad"></road>
            </div>
        </Transition>

        <el-dialog draggable v-model="showPerson" title="个人中心" width="50%" height="50%">
            <person />
        </el-dialog>

        <!-- 项目文字介绍 -->
        <el-dialog draggable v-model="showProjectDialog" title="项目介绍" width="50%" height="50%">
            <div class="project-info-container">
                <h2 class="project-title">扬州市运河路隧道应急救援基地项目</h2>
                <div class="project-section">
                    <h3 class="section-title">项目概况</h3>
                    <p>位于运河快速路和万福快速路互通匝道内，主要用于隧道应急人员的办公、应急车辆的停放及应急物质的仓储。</p>
                    <div class="project-data">
                        <div class="data-item">
                            <span class="data-label">总用地面积</span>
                            <span class="data-value">10652.18㎡</span>
                        </div>
                        <div class="data-item">
                            <span class="data-label">总建筑面积</span>
                            <span class="data-value">4077.10㎡</span>
                        </div>
                        <div class="data-item">
                            <span class="data-label">地上建筑面积</span>
                            <span class="data-value">4077.10㎡</span>
                        </div>
                    </div>
                </div>

                <div class="project-section">
                    <h3 class="section-title">功能布局</h3>
                    <div class="floor-info">
                        <div class="floor">
                            <h4>上层主要功能区</h4>
                            <ul>
                                <li>办公室</li>
                                <li>会议室</li>
                                <li>接待室</li>
                                <li>食堂和厨房</li>
                                <li>文印室</li>
                                <li>母婴室</li>
                                <li>支部活动室</li>
                            </ul>
                        </div>
                        <div class="floor">
                            <h4>下层主要功能区</h4>
                            <ul>
                                <li>资料室</li>
                                <li>档案室</li>
                                <li>机房</li>
                                <li>仓储空间</li>
                                <li>交通空间</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="project-section">
                    <h3 class="section-title">设计愿景</h3>
                    <p>打造市政公共服务设施<span class="highlight">近零能耗标杆项目</span>，可参观、可复制。</p>
                    <p>项目创新性引入：</p>
                    <ul class="feature-list">
                        <li>单晶硅光伏发电</li>
                        <li>钙钛矿光伏</li>
                        <li>碲化镉和储能电池组</li>
                    </ul>
                    <p>并网后对电网的需求侧响应指令作出反应，并且用光伏可再生能源对新能源汽车进行充电。</p>
                </div>

                <div class="project-section">
                    <h3 class="section-title">智慧运维目标</h3>
                    <p class="goal">提升项目的低碳、绿色、健康、智慧水平</p>
                    <div class="goal-items">
                        <div class="goal-item">
                            <div class="goal-number">01</div>
                            <div class="goal-content">构建基于建筑全生命周期BIM技术应用的低碳智慧建筑运维管理平台</div>
                        </div>
                        <div class="goal-item">
                            <div class="goal-number">02</div>
                            <div class="goal-content">打造可感知、可学习、可控制的低碳智慧建筑</div>
                        </div>
                    </div>
                </div>

                <div class="project-section">
                    <h3 class="section-title">智慧运行管理重点</h3>
                    <div class="smart-items">
                        <div class="smart-item">
                            <div class="smart-icon">01</div>
                            <div class="smart-title">空间设施管理</div>
                            <div class="smart-desc">营造安全可靠，便捷高效的建筑空间</div>
                        </div>
                        <div class="smart-item">
                            <div class="smart-icon">02</div>
                            <div class="smart-title">能源和碳排放管理</div>
                            <div class="smart-desc">通过光储直柔系统和能源碳排放监测系统实现对低碳目标的动态评估与调控</div>
                        </div>
                        <div class="smart-item">
                            <div class="smart-icon">03</div>
                            <div class="smart-title">建筑环境管理</div>
                            <div class="smart-desc">打造健康舒适的办公环境</div>
                        </div>
                    </div>
                </div>

                <div class="project-section">
                    <h3 class="section-title">总体思路</h3>
                    <p>引入绿色节能低碳智慧理念，将若干个相互独立、相互关联的系统集成到一个统一的、协调运行的系统中，实现信息资源的优化管理和共享，通过建筑的智慧运维管理，为管理者创造最佳的信息服务，创造低碳、高效、舒适、健康的环境。
                    </p>
                </div>

                <div class="project-section">
                    <h3 class="section-title">实施路径</h3>
                    <div class="path-container">
                        <div class="path-item">
                            <div class="path-title">感知层</div>
                            <div class="path-content">通过传感装置对建筑环境、设备设施、能源系统等进行监测</div>
                        </div>
                        <div class="path-item">
                            <div class="path-title">应用层</div>
                            <div class="path-content">通过楼宇控制系统实现机电系统、能源系统的自动控制</div>
                        </div>
                        <div class="path-item">
                            <div class="path-title">中枢层</div>
                            <div class="path-content">集合楼宇自控系统、低碳智慧管理系统、展示系统，通过BIM孪生平台实现对建筑的低碳智慧综合管理</div>
                        </div>
                    </div>
                    <p class="path-summary">运行数据通过标准接口和符合标准的通信协议送到智慧运维管理平台，按照平台的指令改变运行状态或运行方式，实现优化控制、管理。</p>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script setup>
import dayjs from 'dayjs'

import {
    getCookie,
    removeToken,
    setCookie
} from "@/utils/cookie";
import alarmList from "./components/alarmList.vue";
import alarmDialog from "./components/alarmDialog.vue";
import weatherData from "@/data/weather.js";
import Weather from './components/weather.vue'
import road from './components/road.vue'
import person from '@/views/Person/index.vue'
import { useAppStore } from '@/stores/app';
import headBg from '@/assets/images/head/head.png';
import useSocket from '@/hooks/socket';
import { useMitt } from '@/hooks/mitt';
import axios from 'axios';

const { socketOn, socketEmit } = useSocket()

const userName = ref("");
const timer = ref(null);
const projectName = ref("德福科技研发大楼");
const socket = ref(null);
const mesCount = ref(0);
const dialogVisible = ref(false);
const sockets = ref(null);
const dialogData = ref({
    title: "报警联动",
    visible: false,
    guid: "",
    form1: "",
    video1: "",
    video2: "",
    alarm: {
        deviceName: "",
        standardName: "",
        value: "",
        auto: false,
    },
});
const project = ref([]);
const weather = ref('晴');
const temperature = ref(20);
const date = ref("");
const mode = ref(0);
const weatherPng = ref(new URL('@/views/Layout/img/晴.png', import.meta.url).href);
const showTip = ref(false);
const showWeather = ref(false);
const showRoad = ref(false);
const alarmTotal = ref(0);
const interval = ref(null);
const showPerson = ref(false);
const showProjectDialog = ref(false);


const api = inject('$api')

const router = useRouter();
const store = useAppStore();

const emitter = useMitt();


const emiter = useMitt();
socket.value = inject("socket");

const isClose = computed(() => {
    return store.isClose
})

const messageData = computed(() => {
    return store.mesData;
});

const alarms = computed(() => {
    let arr = [...store.r_alarm, ...store.h_alarm];

    return arr;
});
watch(messageData, (val) => {
    mesCount.value = val.length;
});

onBeforeUnmount(() => {
    if (timer.value) {
        clearInterval(timer.value) // 清除定时器
    }
    if (interval.value) {
        clearInterval(interval.value)
    }
})

// 定时器
timer.value = setInterval(() => {
    date.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}, 1000)


onMounted(() => {

    sessionStorage.setItem("netWeather", "true");

    userName.value = getCookie("gh_name");
    socketEmit("GetAdviceMes", getCookie("gh_id"));
    if (getCookie("gh_projectId") != "0")
        socketEmit("SendHisAlarm", getCookie("gh_projectId"));
    if (getCookie("gh_projectId")) {
        socketEmit("SetProject", getCookie("gh_projectId"));
    }
    getProjectPage()
    // 项目名称
    if (getCookie("gh_projectId") != 0) {
        getProject(getCookie("gh_projectId"));
    }
    if (getCookie("gh_projectId") != "0")
        socketEmit("SendHisAlarm", getCookie("gh_projectId"), getCookie("gh_id"));

    emiter.miitOn("stopAudio", (tag) => {
        if (tag) {
            document.getElementsByTagName("audio")[0].pause();
        }
        if (isClose.value) {
            document.getElementsByTagName("audio")[0].pause();

        } else if (!isClose.value && alarmTotal.value > 0) {

            document.getElementsByTagName("audio")[0].play();
        }
    });
    emiter.miitOn("stopAudio1", () => {
        if (document.getElementsByTagName("audio").length > 0)
            document.getElementsByTagName("audio")[0].pause();
    });
    emiter.miitOn("showLink", (data) => {
        if (data) {
            if (dialogData.value) {
                dialogData.value.visible = true;
                dialogData.value = Object.assign(dialogData.value, data);
                dialogData.value.title = "报警联动";
            }
        }
    })
    // 点击弹窗以外的其他区域关闭弹窗
    document.addEventListener('mouseup', (e) => {
        const tree = document.querySelector('#treeWrap')
        if (tree) {
            if (!tree.contains(e.target)) {
                dialogVisible.value = false
            }
        }
    })

});
// 查询项目
const getProjectPage = () => {
    api.getProject({
        removed: false
    }).then(res => {
        const data = res.data
        project.value = data
    })
}
const goHome = () => {
    store.SET_MENU_INDEX(0)
    router.push({
        path: "/",
    });
}

const userCommand = (command) => {
    switch (command) {
        case "personal":
            showPerson.value = true;

            break;
        case "system":
            console.log(process.env.NODE_ENV)
            window.open(process.env.NODE_ENV === 'production' ? window.PROD_DATA_WEB : window.DEV_DATA_WEB)
            break;
        case "logout": // 退出登录
            loginOut();
            break;
    }
}
// 退出登录
const loginOut = () => {
  
    // emitter.miitEmit('ue', {
    //     type: 'quit',
    //     value: true
    // })

    api.logout().then((res) => {
        removeToken("gh_token");
        router.push({
            path: "/login",
        });
    });
}

const getProject = (id) => {
    api.getProjectById(id).then((res) => {
        if (res.data) {
            projectName.value = res.data.name;

            if (res.data.area) {
                axios
                    .get(`https://restapi.amap.com/v3/weather/weatherInfo?city=${res.data.area}&key=504581ecfd67cb6d006010facdb7a3c8`)
                    .then((result) => {
                        if (result.data.lives && result.data.lives.length > 0) {
                            temperature.value = result.data.lives[0].temperature;
                            weather.value = result.data.lives[0].weather;
                            let path = weatherData[result.data.lives[0].weather];
                            weatherPng.value = new URL(path, import.meta.url).href;
                        }
                    })
                    .catch((error) => {
                        console.log(error)
                    })
            }
        }
    });
}

const showAlert = () => {
    dialogVisible.value = !dialogVisible.value;
}

const showMessage = () => {
    let item = {
        id: null,
        name: "",
        component: "personal",
        secondComponent: null,
        code: "", //菜单编码
        hideCondition: null,
        showCondition: null,
        diagramId: null,
        popName: null,
        model: null,
    }
    setCookie('funMenus', JSON.stringify(item))
    store.SET_FUN_MENU(item)
};

const changeMode = (modeValue) => {
    mode.value = modeValue;
    emitter.miitEmit('changeMode', {
        mode: modeValue,
        tag: true //是否是头部自己切换模式
    });
}

//天气场景
const clickWeather = () => {
    showWeather.value = !showWeather.value;
}
//漫游场景点击
const clickRoad = () => {
    showRoad.value = !showRoad.value;
}

const showProjectInfo = () => {
    showProjectDialog.value = !showProjectDialog.value;
}
if (interval.value) {
    clearInterval(interval.value)
}
interval.value = setInterval(() => {
    getProject(2);
}, 1000 * 30 * 60);

socketOn("connect", () => {
    socketEmit("GetAdviceMes", getCookie("gh_id"));
    if (getCookie("gh_projectId")) {
        socketEmit("SetProject", getCookie("gh_projectId"));
    }
});
socketOn("h_alarm", (res) => {
    if (res) {
        store.SET_ALARM_H(res.records);
        alarmTotal.value = res.total;
        if (res.records.length > 0) {
            nextTick(() => {
                if (!isClose.value) {
                    document.getElementsByTagName("audio")[0].play();
                }
            });
        } else if (res.records.length == 0) {
            //关闭列表
            dialogData.value.visible = false;
        }
    } else {
        alarmTotal.value = 0;
    }
})

socketOn("reconnect", () => {
    if (getCookie("gh_id")) {
        socketEmit("SetUserId", getCookie("gh_id"));
    }
    if (getCookie("gh_projectId")) {
        socketEmit("SetProject", getCookie("gh_projectId"));
    }
})

socketOn("alarm", (res) => {
    socketEmit("SendHisAlarm", getCookie("gh_projectId"));
})

socketOn("ai_alarm", (res) => {
    emiter.miitEmit("ai_alarm", res);
})

socketOn("Switch_System", (res) => {
    emiter.miitEmit("Switch_System", res);
})

socketOn("Weather_Simulation", (res) => {
    let data = res;

    if (window.cmd != data.selectedFloorId) {
        return;
    }
    let val = data.command.toLowerCase()
    if (val == "sunny") {
        val = "clear_skies"
    }
    else if (val == "cloudy") {
        val = "cloudy"
    } else if (val == "overcast") {
        val = "overcast"

    } else if (val == "light rain") {
        val = "rain_light"

    } else if (val == "heavy rain") {
        val = "rain"

    } else if (val == "snow") {
        val = "snow_b"
    }
    emiter.miitEmit('ue', {
        type: 'weather',
        value: val
    })
})

socketOn("Time_Selection", (res) => {
    let data = res;

    if (window.cmd != data.selectedFloorId) {
        return;
    }
    let time = data.command.split(":")[0].trim();
    if (time[0] == 0) {
        time = time[1]
    }
    emiter.miitEmit('ue', {
        type: 'time',
        value: time
    })
})


socketOn("Switch_Floor", (res) => {
    emiter.miitEmit("Switch_Floor", res)
})

socketOn("Tour", (res) => {
    let data = res;
    if (window.cmd != data.selectedFloorId) {
        return;
    }

    if (data.command.toLowerCase() == "outdoor") {
        emiter.miitEmit('ue', {
            type: 'path',
            value: 1
        })
        return;
    }



    emiter.miitEmit('ue', {
        type: 'path',
        value: parseInt(data.command.toLowerCase().replace('floor', '').trim()) + 1
    })
})

socketOn("AdviceMes", (res) => {
    if (res) {
        let data = JSON.parse(JSON.stringify(messageData.value));
        let exsit = messageData.value.find(
            (v) => JSON.stringify(v) == JSON.stringify(res)
        );
        if (!exsit) {
            data.push(res);
        }
        store.SET_MESSAGE_DATA(data);
    }
})



</script>

<style lang="scss" scoped>
.layout-header {
    position: absolute;
    z-index: 199;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    background-image: url("@/assets/images/head/head.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .header_left,
    .header_right {
        width: 377px;

        .scene {
            width: 100px;
            height: 34px;
            background: #0A1B29;
            border: 1px solid #28425B;
            border-radius: 17px;
            margin-right: 18px;

            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #B9DDFD;

        }

        .icon {
            height: 30px;
            width: 30px;
            margin-right: 18px;
        }

        .alarm-icon {
            display: flex;
            cursor: pointer;

            .iconfont {
                font-size: 22px;
            }
        }

        @keyframes fade {
            from {
                opacity: 1;
            }

            50% {
                opacity: 0.4;
            }

            to {
                opacity: 1;
            }
        }

        .ld {
            animation: fade 1s infinite;
        }
    }

    .header_left {
        display: flex;
        padding-left: 15px;
        align-items: center;

        .temperature {
            margin: 0 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;

            div:first-of-type,
            div:last-of-type {
                font-family: "Alibaba-PuHuiTi";
                font-size: 14px;
                font-weight: 400;
                color: #ECECEC;
                white-space: nowrap;
            }

        }

        .mode {
            display: flex;
            align-items: center;
        }

    }

    .header_right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 15px;

    }

    .logo {
        .center_container {

            display: flex;
            justify-content: center;
            cursor: pointer;
            height: 70px;
            width: 1014px;


            .title {
                font-size: 28px;
                font-family: "DOUYU";
                font-weight: normal;
                letter-spacing: 5px;
                color: #fff;
                background: linear-gradient(0deg, #c2e8fd 0%, #a5e5fa 50%, #fff 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                padding-top: 15px;
            }

        }
    }

    .vr {
        margin-right: 13px;
    }

    .weather {
        position: absolute;
        left: 10px;
        top: 30px;

    }

    .road {
        position: absolute;
        right: 10px;
        top: 30px;

    }

    .user-name {
        font-size: 14px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: 400;
        color: #ECECEC;
    }

}

.logo1 {
    width: 100px;
    height: 50px;
    margin: 0 auto;
}

.project-info-container {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(10, 110, 205, 0.7);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
    }

    .project-title {
        text-align: center;
        font-size: 24px;
        color: #0A6ECD;
        margin-bottom: 25px;
        position: relative;

        &:after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, #0A6ECD, #5CCDDE);
        }
    }

    .project-section {
        margin-bottom: 30px;

        .section-title {
            font-size: 20px;
            color: #0A6ECD;
            margin-bottom: 15px;
            padding-left: 12px;
            border-left: 4px solid #0A6ECD;
        }

        p {
            line-height: 1.8;
            margin-bottom: 12px;
            color: #ffffff;
            text-indent: 2em;
        }

        .highlight {
            color: #5CCDDE;
            font-weight: bold;
        }
    }

    .project-data {
        display: flex;
        justify-content: space-around;
        margin: 20px 0;

        .data-item {
            text-align: center;

            .data-label {
                display: block;
                font-size: 14px;
                color: #ffffff;
                margin-bottom: 5px;
            }

            .data-value {
                display: block;
                font-size: 18px;
                color: #5CCDDE;
                font-weight: bold;
            }
        }
    }

    .floor-info {
        display: flex;
        justify-content: space-between;

        .floor {
            width: 48%;
            background-color: rgba(10, 110, 205, 0.1);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

            h4 {
                color: #0A6ECD;
                margin-bottom: 10px;
                font-size: 16px;
                text-align: center;
            }

            ul {
                padding-left: 20px;

                li {
                    line-height: 1.8;
                    position: relative;
                    color: #ffffff;
                    padding-left: 5px;

                    &:before {
                        content: "";
                        position: absolute;
                        left: -15px;
                        top: 10px;
                        width: 6px;
                        height: 6px;
                        background-color: #0A6ECD;
                        border-radius: 50%;
                    }
                }
            }
        }
    }

    .feature-list {
        margin: 15px 0;
        padding-left: 20px;

        li {
            line-height: 1.8;
            position: relative;
            color: #ffffff;

            &:before {
                content: "";
                position: absolute;
                left: -15px;
                top: 10px;
                width: 6px;
                height: 6px;
                background-color: #0A6ECD;
                border-radius: 50%;
            }
        }
    }

    .goal {
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        color: #5CCDDE;
        margin: 15px 0;
    }

    .goal-items {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin: 20px 0;

        .goal-item {
            display: flex;
            align-items: center;
            background-color: rgba(10, 110, 205, 0.1);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

            .goal-number {
                font-size: 24px;
                font-weight: bold;
                color: #0A6ECD;
                margin-right: 15px;
                min-width: 40px;
            }

            .goal-content {
                flex: 1;
                line-height: 1.6;
                color: #ffffff;
            }
        }
    }

    .smart-items {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin: 20px 0;

        .smart-item {
            flex: 1;
            min-width: 200px;
            background-color: rgba(10, 110, 205, 0.1);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

            .smart-icon {
                width: 30px;
                height: 30px;
                background-color: #0A6ECD;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 10px;
            }

            .smart-title {
                font-weight: bold;
                margin-bottom: 8px;
                color: #0A6ECD;
            }

            .smart-desc {
                font-size: 14px;
                line-height: 1.6;
                color: #ffffff;
            }
        }
    }

    .path-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin: 20px 0;

        .path-item {
            background-color: rgba(10, 110, 205, 0.1);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

            .path-title {
                font-weight: bold;
                color: #0A6ECD;
                margin-bottom: 8px;
            }

            .path-content {
                line-height: 1.6;
                color: #ffffff;
            }
        }
    }

    .path-summary {
        background-color: rgba(10, 110, 205, 0.15);
        padding: 15px;
        border-radius: 8px;
        text-indent: 0;
        font-style: italic;
        color: #ffffff;
    }
}
</style>
