<template>
    <div class="modern-login">
        <!-- 动态背景特效 -->
        <div class="bg-animation">
            <div class="bg-gradient"></div>
            
            <!-- 粒子系统 -->
            <div class="particles-container">
                <div class="particle" v-for="n in 50" :key="'particle-' + n" 
                     :style="getParticleStyle(n)"></div>
            </div>
            
            <!-- 动态线条网格 -->
            <div class="grid-lines">
                <div class="line horizontal" v-for="n in 20" :key="'h-' + n" 
                     :style="getHorizontalLineStyle(n)"></div>
                <div class="line vertical" v-for="n in 20" :key="'v-' + n" 
                     :style="getVerticalLineStyle(n)"></div>
            </div>
            
            <!-- 数据流效果 -->
            <div class="data-streams">
                <div class="stream" v-for="n in 8" :key="'stream-' + n"
                     :style="getStreamStyle(n)">
                    <div class="stream-dot" v-for="m in 5" :key="'dot-' + m"
                         :style="getStreamDotStyle(m)"></div>
                </div>
            </div>
            
            <!-- 脉冲光环 -->
            <div class="pulse-rings">
                <div class="pulse-ring" v-for="n in 3" :key="'ring-' + n"
                     :style="getPulseRingStyle(n)"></div>
            </div>
            
            <!-- 浮动几何图形 -->
            <div class="floating-shapes">
                <div class="shape triangle" v-for="n in 6" :key="'triangle-' + n"
                     :style="getTriangleStyle(n)"></div>
                <div class="shape circle" v-for="n in 8" :key="'circle-' + n"
                     :style="getCircleStyle(n)"></div>
                <div class="shape hexagon" v-for="n in 4" :key="'hexagon-' + n"
                     :style="getHexagonStyle(n)"></div>
            </div>
            
            <!-- 电路板线条 -->
            <div class="circuit-lines">
                <svg class="circuit-svg" viewBox="0 0 1200 800">
                    <defs>
                        <linearGradient id="circuit-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:rgba(61,233,250,0);stop-opacity:0" />
                            <stop offset="50%" style="stop-color:rgba(61,233,250,0.8);stop-opacity:1" />
                            <stop offset="100%" style="stop-color:rgba(61,233,250,0);stop-opacity:0" />
                        </linearGradient>
                    </defs>
                    <path class="circuit-path circuit-1" d="M0,100 Q300,50 600,150 T1200,100" 
                          stroke="url(#circuit-gradient)" stroke-width="2" fill="none"/>
                    <path class="circuit-path circuit-2" d="M0,300 Q400,250 800,350 T1200,300" 
                          stroke="url(#circuit-gradient)" stroke-width="2" fill="none"/>
                    <path class="circuit-path circuit-3" d="M0,500 Q500,450 900,550 T1200,500" 
                          stroke="url(#circuit-gradient)" stroke-width="2" fill="none"/>
                    <path class="circuit-path circuit-4" d="M0,700 Q200,650 700,750 T1200,700" 
                          stroke="url(#circuit-gradient)" stroke-width="2" fill="none"/>
                </svg>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="login-container">
            <!-- 左侧品牌展示区域 -->
            <div class="brand-section">
                <div class="brand-content">
                    <div class="logo-area">
                        <div class="logo-container">
                            <div class="logo-icon">
                                <!-- <i class="el-icon-office-building"></i> -->
                                <img src="@/assets/images/logo.png" alt="logo" class="logo-img">
                            </div>
                            <div class="logo-glow"></div>
                        </div>
                        <h1 class="brand-title">智慧建筑<br/>运营管理平台</h1>
                        <p class="brand-subtitle">现代化智能建筑管理解决方案</p>
                    </div>
                    
                    <div class="feature-showcase">
                        <div class="feature-item" v-for="(feature, index) in features" :key="index">
                            <div class="feature-icon">
                                <i class="iconfont" :class="feature.icon"></i>
                            </div>
                            <div class="feature-content">
                                <h3>{{ feature.title }}</h3>
                                <p>{{ feature.desc }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录表单区域 -->
            <div class="form-section">
                <div class="form-container">
                    <!-- 登录表单 -->
                    <div class="login-form-wrapper" v-if="!register">
                        <div class="form-header">
                            <h2>欢迎回来</h2>
                            <p>请登录您的账户以继续使用</p>
                        </div>

                        <el-form class="login-form" :model="form" ref="ruleForm" :rules="rules">
                            <el-form-item prop="username">
                                <div class="input-group">
                                    <label class="input-label">用户名</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-user"></i>
                                        <el-input
                                            type="text"
                                            v-model="form.username"
                                            placeholder="请输入用户名"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item prop="password">
                                <div class="input-group">
                                    <label class="input-label">密码</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-lock"></i>
                                        <el-input
                                            type="password"
                                            v-model="form.password"
                                            placeholder="请输入密码"
                                            size="large"
                                            class="modern-input"
                                            show-password>
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item prop="code" v-if="form.username.toLowerCase() === 'demo'">
                                <div class="input-group">
                                    <label class="input-label">邀请码</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-key"></i>
                                        <el-input
                                            type="text"
                                            v-model="form.code"
                                            placeholder="请输入邀请码"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <div class="form-options">
                                <el-checkbox v-model="form.autoLogin" class="remember-me">
                                    记住我
                                </el-checkbox>
                                <a href="#" class="forgot-link">忘记密码？</a>
                            </div>

                            <el-button
                                :loading="isLogin"
                                @click="handleSubmit"
                                type="primary"
                                class="login-btn"
                                size="large">
                                <span v-if="!isLogin">
                                    <i class="el-icon-right"></i>
                                    立即登录
                                </span>
                                <span v-else>登录中...</span>
                            </el-button>
                        </el-form>

                        <div class="form-footer">
                            <p class="register-prompt">
                                还没有账户？
                                <a href="#" @click="clickRegister" class="register-link">立即注册</a>
                            </p>
                        </div>
                    </div>

                    <!-- 注册表单 -->
                    <div class="register-form-wrapper" v-if="register">
                        <div class="form-header">
                            <button class="back-btn" @click="back">
                                <i class="el-icon-arrow-left"></i>
                            </button>
                            <h2>创建新账户</h2>
                            <p>请填写以下信息完成注册</p>
                        </div>

                        <el-form class="register-form" :model="userForm" ref="registerForm" :rules="regRules">
                            <el-form-item prop="name">
                                <div class="input-group">
                                    <label class="input-label">用户名</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-user"></i>
                                        <el-input
                                            type="text"
                                            v-model="userForm.name"
                                            placeholder="请输入用户名"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item prop="userName">
                                <div class="input-group">
                                    <label class="input-label">登录名</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-user-solid"></i>
                                        <el-input
                                            type="text"
                                            v-model="userForm.userName"
                                            placeholder="请输入登录名"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item prop="phone">
                                <div class="input-group">
                                    <label class="input-label">手机号</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-phone"></i>
                                        <el-input
                                            v-model.number="userForm.phone"
                                            placeholder="请输入手机号"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item prop="email">
                                <div class="input-group">
                                    <label class="input-label">邮箱</label>
                                    <div class="input-wrapper">
                                        <i class="input-icon el-icon-message"></i>
                                        <el-input
                                            v-model="userForm.email"
                                            placeholder="请输入邮箱"
                                            size="large"
                                            class="modern-input">
                                        </el-input>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-button
                                :loading="isLogin"
                                @click="handleRegister"
                                type="primary"
                                class="register-btn"
                                size="large">
                                <span v-if="!isLogin">
                                    <i class="el-icon-check"></i>
                                    立即注册
                                </span>
                                <span v-else>注册中...</span>
                            </el-button>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>

        <Verify @success="login" @close="close" :captchaType="'blockPuzzle'"
            :imgSize="{ width: '400px', height: '200px' }" ref="verify">
        </Verify>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';
import Verify from '@/components/verifition/Verify.vue';
import {
    encode
} from 'js-base64';
import {
    getCookie,setCookie
} from '@/utils/cookie';
import {
    ElMessage
} from 'element-plus';
import { useAppStore } from '@/stores/app';
import useSocket from '@/hooks/socket';

const api = inject('$api')
const store = useAppStore();
const router = useRouter();
const registerForm = ref();
const register = ref(false);
const verify = ref();
const ruleForm = ref();

// 功能特性数据
const features = ref([
    {
        icon: 'iconrijundengxiaoshi',
        title: '实时监控',
        desc: '7×24小时设备状态监控'
    },
    {
        icon: 'iconrengongzhineng3',
        title: '智能分析',
        desc: '大数据分析与预测'
    },
    {
        icon: 'iconzidonghuafenxi',
        title: '自动化管理',
        desc: '智能化运维管理'
    }
]);

// 动态特效样式生成函数
const getParticleStyle = (index) => {
    const size = Math.random() * 4 + 1;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    const duration = Math.random() * 20 + 10;
    const delay = Math.random() * 5;
    
    return {
        width: size + 'px',
        height: size + 'px',
        left: x + '%',
        top: y + '%',
        animationDuration: duration + 's',
        animationDelay: delay + 's'
    };
};

const getHorizontalLineStyle = (index) => {
    const y = (index / 20) * 100;
    const delay = Math.random() * 10;
    
    return {
        top: y + '%',
        animationDelay: delay + 's'
    };
};

const getVerticalLineStyle = (index) => {
    const x = (index / 20) * 100;
    const delay = Math.random() * 10;
    
    return {
        left: x + '%',
        animationDelay: delay + 's'
    };
};

const getStreamStyle = (index) => {
    const angle = (index / 8) * 360;
    const x = Math.random() * 80 + 10;
    const y = Math.random() * 80 + 10;
    
    return {
        left: x + '%',
        top: y + '%',
        transform: `rotate(${angle}deg)`,
        animationDelay: (index * 0.5) + 's'
    };
};

const getStreamDotStyle = (index) => {
    return {
        animationDelay: (index * 0.2) + 's'
    };
};

const getPulseRingStyle = (index) => {
    const size = 100 + index * 50;
    const delay = index * 2;
    
    return {
        width: size + 'px',
        height: size + 'px',
        animationDelay: delay + 's'
    };
};

const getTriangleStyle = (index) => {
    const size = Math.random() * 20 + 10;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    const duration = Math.random() * 30 + 20;
    const delay = Math.random() * 10;
    
    return {
        left: x + '%',
        top: y + '%',
        borderBottomWidth: size + 'px',
        borderLeftWidth: size/2 + 'px',
        borderRightWidth: size/2 + 'px',
        animationDuration: duration + 's',
        animationDelay: delay + 's'
    };
};

const getCircleStyle = (index) => {
    const size = Math.random() * 30 + 10;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    const duration = Math.random() * 25 + 15;
    const delay = Math.random() * 8;
    
    return {
        width: size + 'px',
        height: size + 'px',
        left: x + '%',
        top: y + '%',
        animationDuration: duration + 's',
        animationDelay: delay + 's'
    };
};

const getHexagonStyle = (index) => {
    const size = Math.random() * 25 + 15;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    const duration = Math.random() * 35 + 25;
    const delay = Math.random() * 12;
    
    return {
        width: size + 'px',
        height: size + 'px',
        left: x + '%',
        top: y + '%',
        animationDuration: duration + 's',
        animationDelay: delay + 's'
    };
};

const form = ref({
    username: '',
    password: '',
    code: '',
    params: '',
    autoLogin: true
})
const userForm = ref({
    userName: '',
    name: '',
    phone: '',
    email: ''
})

const isLogin = ref(false);

const rules = ref({
    username: [{
        required: true,
        message: '请输入账号',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    code: [{
        required: true,
        message: '请输入邀请码',
        trigger: 'blur'
    }],
})

const regRules = ref({
    userName: [{
        required: true,
        message: '请输入登录名',
        trigger: 'blur'
    }],
    name: [{
        required: true,
        message: '请输入用户名',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    email: [{
        required: true,
        message: '请输入邮箱',
        trigger: 'blur'
    }, {
        validator: (_, value, callback) => {
            const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的邮箱'));
            }
        },
        trigger: 'blur'
    }],
    phone: [{
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
    }, {
        validator: (_, value, callback) => {
            const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入正确的手机号码"));
            }
        },
        trigger: 'blur'
    }],
})

const { socketEmit } = useSocket()

const handleSubmit = () => {
    ruleForm.value.validate((valid) => {
        if (valid) {
            isLogin.value = true;
            verify.value.show();
        }
    });
};

const clickRegister = () => {
    register.value = true
}

// 注册
const handleRegister = () => {
    registerForm.value.validate((valid) => {
        if (valid) {
            api.register(userForm.value).then(res => {
                if (res.success) {
                    register.value = false;
                    ElMessage({
                        message: '注册成功',
                        type: 'success'
                    });
                    router.push({
                        path: '/login'
                    })
                }
            })
        } else {
            return false
        }
    })
}

// 登录
const login = async () => {
    try {
        const { data } = await api.login({
            username: encode(form.value.username),
            password: encode(form.value.password),
            code: form.value.code,
            params: { captchaVerification: null }
        });

        store.SET_TOKEN(data.access_token);
        setCookie("gh_token", data.access_token);
        setCookie("gh_id", data.userId);
        setCookie("gh_name", data.name);
        setCookie("_refreshToken", data.refresh_token);

        const res = await api.getDefaultProject()
        setCookie("gh_projectId", res.data);
        store.SET_PROJECT_ID(res.data)

        router.push({
            path: '/'
        })
        socketEmit('SetProject', getCookie('gh_projectId'));
        socketEmit('SetUserId', getCookie('gh_id'));
    } catch (e) {
        isLogin.value = false;
    }
};

const back = () => {
    register.value = false
}

const close = () => {
    isLogin.value = false;
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.modern-login {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Alibaba-PuHuiTi', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    // 动态背景特效
    .bg-animation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        .bg-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('@/assets/images/home.jpg') no-repeat center fixed;
            background-size: cover;
            
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(2, 20, 36, 0.8) 0%, rgba(16, 52, 87, 0.9) 100%);
            }
        }

        // 粒子系统
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;

            .particle {
                position: absolute;
                background: radial-gradient(circle, rgba(61, 233, 250, 0.8) 0%, rgba(61, 233, 250, 0.2) 50%, transparent 100%);
                border-radius: 50%;
                animation: particleFloat 20s infinite linear;
                box-shadow: 0 0 6px rgba(61, 233, 250, 0.4);

                &:nth-child(odd) {
                    animation-direction: reverse;
                }

                &:hover {
                    animation-play-state: paused;
                    transform: scale(1.5);
                    box-shadow: 0 0 12px rgba(61, 233, 250, 0.8);
                }
            }
        }

        // 动态线条网格
        .grid-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;

            .line {
                position: absolute;
                background: linear-gradient(90deg, transparent 0%, rgba(61, 233, 250, 0.3) 50%, transparent 100%);
                animation: lineMove 15s infinite linear;

                &.horizontal {
                    width: 100%;
                    height: 1px;
                    animation-name: lineHorizontal;

                    &:nth-child(even) {
                        animation-direction: reverse;
                    }
                }

                &.vertical {
                    width: 1px;
                    height: 100%;
                    animation-name: lineVertical;

                    &:nth-child(odd) {
                        animation-direction: reverse;
                    }
                }
            }
        }

        // 数据流效果
        .data-streams {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .stream {
                position: absolute;
                width: 200px;
                height: 2px;
                animation: streamMove 8s infinite linear;

                .stream-dot {
                    position: absolute;
                    width: 4px;
                    height: 4px;
                    background: rgba(61, 233, 250, 0.8);
                    border-radius: 50%;
                    box-shadow: 0 0 8px rgba(61, 233, 250, 0.6);
                    animation: dotMove 2s infinite linear;

                    &:nth-child(1) { left: 0%; }
                    &:nth-child(2) { left: 25%; }
                    &:nth-child(3) { left: 50%; }
                    &:nth-child(4) { left: 75%; }
                    &:nth-child(5) { left: 100%; }
                }
            }
        }

        // 脉冲光环
        .pulse-rings {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            .pulse-ring {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                border: 2px solid rgba(61, 233, 250, 0.3);
                border-radius: 50%;
                animation: pulseRing 6s infinite ease-out;
            }
        }

        // 浮动几何图形
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .triangle {
                position: absolute;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 20px solid rgba(61, 233, 250, 0.2);
                border-radius: 0;
                animation: shapeFloat 25s infinite ease-in-out;
                filter: drop-shadow(0 0 4px rgba(61, 233, 250, 0.4));

                &:nth-child(odd) {
                    animation-direction: reverse;
                }
            }

            .circle {
                position: absolute;
                border: 2px solid rgba(61, 233, 250, 0.3);
                border-radius: 50%;
                background: radial-gradient(circle, rgba(61, 233, 250, 0.1) 0%, transparent 70%);
                animation: shapeFloat 20s infinite ease-in-out;
                
                &:nth-child(even) {
                    animation-direction: reverse;
                }
            }

            .hexagon {
                position: absolute;
                background: rgba(61, 233, 250, 0.2);
                border-radius: 6px;
                animation: shapeFloat 30s infinite ease-in-out;
                
                &:before {
                    content: '';
                    position: absolute;
                    top: -8px;
                    left: 0;
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-bottom: 8px solid rgba(61, 233, 250, 0.2);
                }

                &:after {
                    content: '';
                    position: absolute;
                    bottom: -8px;
                    left: 0;
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-top: 8px solid rgba(61, 233, 250, 0.2);
                }
            }
        }

        // 电路板线条
        .circuit-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.6;

            .circuit-svg {
                width: 100%;
                height: 100%;

                .circuit-path {
                    stroke-dasharray: 20, 10;
                    animation: circuitFlow 12s infinite linear;

                    &.circuit-1 {
                        animation-delay: 0s;
                    }

                    &.circuit-2 {
                        animation-delay: 3s;
                    }

                    &.circuit-3 {
                        animation-delay: 6s;
                    }

                    &.circuit-4 {
                        animation-delay: 9s;
                    }
                }
            }
        }
    }

    // 主容器
    .login-container {
        position: relative;
        z-index: 2;
        width: 90%;
        max-width: 1100px;
        height: 700px;
        // background: url("/src/assets/images/dialog/panel_bg.png") no-repeat;
        // background-size: 100% 100%;
        border: 1px solid rgba(61, 233, 250, 0.3);
        border-radius: 15px;
        backdrop-filter: blur(5px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        display: flex;
        overflow: hidden;
        animation: slideInUp 0.8s ease-out;

        // 左侧品牌展示区域
        .brand-section {
            flex: 1;
            background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            border-right: 1px solid rgba(61, 233, 250, 0.2);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(61,233,250,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
                opacity: 0.3;
            }

            .brand-content {
                position: relative;
                z-index: 1;
                text-align: center;
                color: #E6F4FF;

                .logo-area {
                    margin-bottom: 60px;

                    .logo-container {
                        position: relative;
                        display: inline-block;
                        margin-bottom: 30px;

                        .logo-icon {
                            width: 100px;
                            height: 100px;
                            background: transparent;
                            border: none;
                            border-radius: 20px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            position: relative;
                            z-index: 2;
                            overflow: hidden;

                            .logo-img {
                                width: 100px;
                                height: 100px;
                                object-fit: contain;
                            }

                            i {
                                font-size: 40px;
                                color: $themeColor;
                                text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);
                            }
                        }

                        .logo-glow {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 120px;
                            height: 120px;
                            background: radial-gradient(circle, rgba(61, 233, 250, 0.3) 0%, transparent 70%);
                            border-radius: 50%;
                            animation: pulse 3s infinite;
                        }
                    }

                    .brand-title {
                        font-size: 36px;
                        font-family: "DOUYU";
                        font-weight: 700;
                        margin: 0 0 16px 0;
                        line-height: 1.2;
                        background: linear-gradient(135deg, #E6F4FF 0%, #3de9fa 50%, #FFFFFF 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        text-shadow: 0 0 20px rgba(61, 233, 250, 0.3);
                        letter-spacing: 2px;
                        position: relative;
                        animation: titleGlow 3s ease-in-out infinite alternate;

                        &::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: linear-gradient(135deg, transparent 0%, rgba(61, 233, 250, 0.1) 50%, transparent 100%);
                            animation: titleShine 4s ease-in-out;
                            z-index: -1;
                        }
                    }

                    .brand-subtitle {
                        font-size: 18px;
                        color: #B8C5D1;
                        margin: 0;
                        font-weight: 400;
                        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                        opacity: 0.9;
                        animation: subtitleFade 2s ease-in-out;
                    }
                }

                .feature-showcase {
                    .feature-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 24px;
                        padding: 20px;
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
                        border: 1px solid rgba(61, 233, 250, 0.3);
                        border-radius: 12px;
                        backdrop-filter: blur(8px);
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2),
                            inset 0 1px 0 rgba(255, 255, 255, 0.05);
                        transition: all 0.3s ease;
                        position: relative;
                        overflow: hidden;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
                            opacity: 0;
                            transition: opacity 0.3s ease;
                        }

                        &:hover {
                            transform: translateY(-2px);
                            border-color: rgba(61, 233, 250, 0.5);
                            box-shadow: 0 6px 20px rgba(61, 233, 250, 0.2),
                                inset 0 1px 0 rgba(255, 255, 255, 0.1);

                            &::before {
                                opacity: 1;
                            }
                        }

                        .feature-icon {
                            width: 50px;
                            height: 50px;
                            background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                            border: 1px solid rgba(61, 233, 250, 0.3);
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 16px;
                            flex-shrink: 0;
                            backdrop-filter: blur(4px);

                            .iconfont {
                                font-family: "iconfont" !important;
                                font-size: 24px !important;
                                color: $themeColor;
                                text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);
                                font-weight: normal;
                                font-style: normal;
                                -webkit-font-smoothing: antialiased;
                                -moz-osx-font-smoothing: grayscale;
                                display: inline-block;
                                line-height: 1;
                            }

                            i {
                                font-size: 24px;
                                color: $themeColor;
                                text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);
                            }
                        }

                        .feature-content {
                            text-align: left;

                            h3 {
                                font-size: 16px;
                                font-weight: 600;
                                margin: 0 0 4px 0;
                                background: linear-gradient(135deg, #E6F4FF 0%, #3de9fa 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                                position: relative;
                                animation: featureTitleGlow 2s ease-in-out infinite alternate;

                                &::after {
                                    content: '';
                                    position: absolute;
                                    bottom: -2px;
                                    left: 0;
                                    width: 100%;
                                    height: 1px;
                                    background: linear-gradient(90deg, #3de9fa 0%, transparent 100%);
                                    opacity: 0.5;
                                }
                            }

                            p {
                                font-size: 14px;
                                color: #B8C5D1;
                                margin: 0;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                            }
                        }
                    }
                }
            }
        }

        // 右侧表单区域
        .form-section {
            flex: 1;
            background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(61,233,250,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
                opacity: 0.3;
            }

            .form-container {
                position: relative;
                z-index: 1;
                width: 100%;
                max-width: 380px;

                .form-header {
                    text-align: center;
                    margin-bottom: 40px;
                    position: relative;

                    .back-btn {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 44px;
                        height: 44px;
                        border: 1px solid rgba(61, 233, 250, 0.3);
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(4px);

                        &:hover {
                            border-color: $themeColor;
                            background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                            transform: translateX(-2px);
                            box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2);
                        }

                        i {
                            font-size: 20px;
                            color: #E6F4FF;
                        }
                    }

                    h2 {
                        font-size: 32px;
                        font-family: "DOUYU";
                        font-weight: 700;
                        margin: 0 0 8px 0;
                        letter-spacing: -0.5px;
                        background: linear-gradient(135deg, #E6F4FF 0%, #3de9fa 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        position: relative;
                        animation: formTitleSlide 0.8s ease-out;

                        &::before {
                            content: '';
                            position: absolute;
                            bottom: -4px;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 60px;
                            height: 2px;
                            background: linear-gradient(90deg, transparent 0%, #3de9fa 50%, transparent 100%);
                            animation: underlineExpand 1s ease-out 0.5s both;
                        }
                    }

                    p {
                        color: #B8C5D1;
                        font-size: 16px;
                        margin: 0;
                        font-weight: 400;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        animation: formSubtitleFade 1s ease-out 0.3s both;
                        opacity: 0;
                    }
                }

                // 表单样式
                .login-form, .register-form {
                    .input-group {
                        margin-bottom: 24px;

                        .input-label {
                            display: block;
                            font-size: 14px;
                            font-weight: 600;
                            margin-bottom: 8px;
                            letter-spacing: 0.025em;
                            background: linear-gradient(135deg, #E6F4FF 0%, #B8C5D1 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                            position: relative;
                            animation: labelSlide 0.6s ease-out;

                            &::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: -2px;
                                width: 2px;
                                height: 100%;
                                background: linear-gradient(180deg, #3de9fa 0%, transparent 100%);
                                opacity: 0.7;
                            }
                        }

                        .input-wrapper {
                            position: relative;

                            .input-icon {
                                position: absolute;
                                left: 16px;
                                top: 50%;
                                transform: translateY(-50%);
                                color: #7A9BBD;
                                font-size: 18px;
                                z-index: 2;
                                transition: color 0.3s ease;
                            }

                            :deep(.el-input) {
                                .el-input__wrapper {
                                    background: rgba(2, 20, 36, 0.8) !important;
                                    border: 1px solid rgba(61, 233, 250, 0.4) !important;
                                    border-radius: 8px !important;
                                    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
                                    transition: all 0.3s ease !important;
                                    padding-left: 52px;

                                    &:hover {
                                        border-color: rgba(61, 233, 250, 0.6) !important;
                                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
                                    }

                                    &.is-focus {
                                        border-color: $themeColor !important;
                                        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4) !important;
                                    }
                                }

                                .el-input__inner {
                                    color: #7A9BBD !important;
                                    font-size: 16px;
                                    font-family: "Alibaba-PuHuiTi";
                                    font-weight: 400;
                                    padding-left: 52px;

                                    &::placeholder {
                                        color: rgba(122, 155, 189, 0.6) !important;
                                    }
                                }
                            }

                            &:focus-within .input-icon {
                                color: $themeColor;
                            }
                        }
                    }

                    :deep(.el-form-item) {
                        margin-bottom: 0;
                    }
                }

                .form-options {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 32px;

                    :deep(.el-checkbox) {
                        .el-checkbox__label {
                            color: #B8C5D1;
                            font-size: 14px;
                            font-weight: 500;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        }

                        .el-checkbox__input.is-checked .el-checkbox__inner {
                            background-color: $themeColor;
                            border-color: $themeColor;
                        }

                        .el-checkbox__inner {
                            border-radius: 4px;
                            border-color: rgba(61, 233, 250, 0.4);
                            background: rgba(2, 20, 36, 0.8);
                        }
                    }

                    .forgot-link {
                        color: $themeColor;
                        text-decoration: none;
                        font-size: 14px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);

                        &:hover {
                            color: #ffffff;
                            text-shadow: 0 0 12px rgba(61, 233, 250, 0.8);
                        }
                    }
                }

                // 登录/注册按钮
                .login-btn, .register-btn {
                    width: 100%;
                    height: 52px;
                    background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                    border: 1px solid rgba(61, 233, 250, 0.3);
                    border-radius: 8px;
                    color: #E6F4FF;
                    font-size: 16px;
                    font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                    font-weight: 600;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(4px);
                    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                    &:hover {
                        transform: translateY(-2px);
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                        border-color: $themeColor;
                        box-shadow: 0 4px 16px rgba(61, 233, 250, 0.3),
                            inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    }

                    &:active {
                        transform: translateY(0);
                    }

                    &.is-loading {
                        opacity: 0.8;
                        cursor: not-allowed;
                    }

                    i {
                        margin-right: 8px;
                        font-size: 18px;
                        color: $themeColor;
                        text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);
                    }
                }

                // 表单底部
                .form-footer {
                    text-align: center;
                    margin-top: 32px;

                    .register-prompt {
                        color: #B8C5D1;
                        font-size: 15px;
                        margin: 0;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

                        .register-link {
                            color: $themeColor;
                            text-decoration: none;
                            font-weight: 600;
                            margin-left: 4px;
                            transition: all 0.3s ease;
                            text-shadow: 0 0 8px rgba(61, 233, 250, 0.5);

                            &:hover {
                                color: #ffffff;
                                text-shadow: 0 0 12px rgba(61, 233, 250, 0.8);
                            }
                        }
                    }
                }
            }
        }
    }

    // 特效动画
    @keyframes particleFloat {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }

    @keyframes lineHorizontal {
        0% {
            transform: translateX(-100%);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    @keyframes lineVertical {
        0% {
            transform: translateY(-100%);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translateY(100%);
            opacity: 0;
        }
    }

    @keyframes streamMove {
        0% {
            transform: translate(-50%, -50%) rotate(var(--rotation, 0deg));
            opacity: 0;
        }
        20% {
            opacity: 1;
        }
        80% {
            opacity: 1;
        }
        100% {
            transform: translate(50%, 50%) rotate(var(--rotation, 0deg));
            opacity: 0;
        }
    }

    @keyframes dotMove {
        0% {
            background: rgba(61, 233, 250, 0.2);
            box-shadow: 0 0 4px rgba(61, 233, 250, 0.2);
        }
        50% {
            background: rgba(61, 233, 250, 1);
            box-shadow: 0 0 12px rgba(61, 233, 250, 0.8);
        }
        100% {
            background: rgba(61, 233, 250, 0.2);
            box-shadow: 0 0 4px rgba(61, 233, 250, 0.2);
        }
    }

    @keyframes pulseRing {
        0% {
            transform: translate(-50%, -50%) scale(0.5);
            opacity: 1;
            border-width: 4px;
        }
        50% {
            opacity: 0.3;
        }
        100% {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0;
            border-width: 1px;
        }
    }

    @keyframes shapeFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.3;
        }
        25% {
            transform: translateY(-20px) rotate(90deg);
            opacity: 0.7;
        }
        50% {
            transform: translateY(-10px) rotate(180deg);
            opacity: 1;
        }
        75% {
            transform: translateY(-30px) rotate(270deg);
            opacity: 0.7;
        }
    }

    @keyframes circuitFlow {
        0% {
            stroke-dashoffset: 0;
            opacity: 0;
        }
        20% {
            opacity: 1;
        }
        80% {
            opacity: 1;
        }
        100% {
            stroke-dashoffset: 200;
            opacity: 0;
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0.6;
            transform: translate(-50%, -50%) scale(1);
        }
        50% {
            opacity: 0.8;
            transform: translate(-50%, -50%) scale(1.1);
        }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes titleGlow {
        0% {
            text-shadow: 0 0 20px rgba(61, 233, 250, 0.3);
        }
        100% {
            text-shadow: 0 0 20px rgba(61, 233, 250, 0.6);
        }
    }

    @keyframes titleShine {
        0% {
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
    }

    @keyframes subtitleFade {
        0% {
            opacity: 0.9;
        }
        100% {
            opacity: 1;
        }
    }

    @keyframes formTitleSlide {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes underlineExpand {
        0% {
            width: 0;
        }
        100% {
            width: 60px;
        }
    }

    @keyframes formSubtitleFade {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }

    @keyframes featureTitleGlow {
        0% {
            text-shadow: 0 0 20px rgba(61, 233, 250, 0.3);
        }
        100% {
            text-shadow: 0 0 20px rgba(61, 233, 250, 0.6);
        }
    }

    @keyframes labelSlide {
        0% {
            transform: translateY(10px);
        }
        100% {
            transform: translateY(0);
        }
    }

    // 响应式设计
    @media (max-width: 1024px) {
        .bg-animation {
            .particles-container .particle {
                display: none;
            }
            
            .circuit-lines {
                opacity: 0.3;
            }
        }

        .login-container {
            width: 95%;
            height: auto;
            min-height: 600px;
            flex-direction: column;

            .brand-section, .form-section {
                flex: none;
                padding: 40px 30px;
                border-right: none;
                border-bottom: 1px solid rgba(61, 233, 250, 0.2);
            }

            .brand-section {
                .brand-content {
                    .logo-area .brand-title {
                        font-size: 28px;
                    }

                    .feature-showcase {
                        display: flex;
                        gap: 16px;
                        flex-wrap: wrap;

                        .feature-item {
                            flex: 1;
                            min-width: 200px;
                            margin-bottom: 16px;
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 768px) {
        .bg-animation {
            .grid-lines,
            .data-streams,
            .floating-shapes {
                display: none;
            }
        }

        .login-container {
            margin: 20px;
            width: calc(100% - 40px);
            border-radius: 12px;

            .brand-section {
                padding: 30px 20px;

                .brand-content {
                    .logo-area .brand-title {
                        font-size: 24px;
                    }

                    .feature-showcase {
                        flex-direction: column;

                        .feature-item {
                            min-width: auto;
                        }
                    }
                }
            }

            .form-section {
                padding: 30px 20px;

                .form-container {
                    max-width: none;
                }
            }
        }
    }
}
</style>
