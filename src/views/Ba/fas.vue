<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='运行统计' />
            <div class="item-body order">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="iconfont icon-device"></i>
                        </div>
                        <div class="stat-number">{{ all }}</div>
                        <div class="stat-label">总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="iconfont icon-play"></i>
                        </div>
                        <div class="stat-number">{{ on }}</div>
                        <div class="stat-label">运行</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="iconfont icon-pause"></i>
                        </div>
                        <div class="stat-number">{{ off }}</div>
                        <div class="stat-label">停止</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="iconfont icon-warning"></i>
                        </div>
                        <div class="stat-number">{{ fault }}</div>
                        <div class="stat-label">故障</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='设备群控' />
            <div class="item-body device-control-list">
                <el-scrollbar v-if="manual.length > 0">
                    <div class="list" v-for="item in manual" :key="item.id">
                        <div class="name">
                            <img src="@/assets/images/common/d3.png" />
                            <div>{{ item.name }}</div>
                        </div>
                        <div class="dot"></div>
                        <div class="btn">
                            <div class="center cursor" @click="writeValue(item, 1)">
                                <div class="center">开</div>
                            </div>
                            <div class="center cursor" @click="writeValue(item, 0)">
                                <div class="center">关</div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='运行事件' />
            <div class="item-body event-list">
                <el-scrollbar v-if="manualLog.length > 0">
                    <div class="list" v-for="(item, index) in manualLog" :key="index">
                        <div class="name">
                            <img v-if="item.newValue > 0" src="@/assets/images/common/on.png" />
                            <img v-else src="@/assets/images/common/off.png" />
                            <div>{{ item.deviceName }}</div>
                        </div>
                        <div class="standard-name">{{ item.standardName }}</div>
                        <div class="value">{{ item.newValue }}</div>
                        <div class="time">{{ item.logTime }}</div>
                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    computed,
    inject,
    watch,
    ref
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";

import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import { useAppStore } from '@/stores/app';

defineOptions({
    name: "ba",
})

const api = inject('$api')

const manual = ref([])
const manualLog = ref([])
const on = ref(0)
const off = ref(0)
const fault = ref(0)
const all = ref(0)


const store = useAppStore()

const activeMenus = computed(() => {
    let menu = getCookie("funMenus");
    return store.funMenus ?
        store.funMenus :
        menu ?
            JSON.parse(menu) :
            "";
});
watch(activeMenus, () => {
    getDeviceTypeStatus();
    getRunConfigPage();
    getRunManualPage()
});

const getRunConfigPage = () => {
    manual.value = [];
    api.getGroup({
        projectId: getCookie("gh_projectId"),
        menuId: activeMenus.value.id
    }).then((res) => {
        manual.value = res.data;
    });
};
const writeValue = (group, status) => {
    ElMessageBox.confirm('是否确认该操作？', '提示', {
        confirmButtonClass: 'confirmBtn',
        cancelButtonClass: 'cancelBtn',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        if (group && group.id) {
            await api.writeGroup({
                groupId: group.id,
                value: status,
            })
            ElMessage.success('操作成功')
            getRunConfigPage();
        }
    })

};

const getRunManualPage = () => {
    api.getDeviceLog({
        projectId: getCookie("gh_projectId"),
        menuId: activeMenus.value.id,
        page: 1,
        size: 10,
    }).then((res) => {
        manualLog.value = res.data;
    });
};

const getDeviceTypeStatus = () => {
    let typeCode = [];
    if (activeMenus.value.name.includes('空调')) {
        typeCode = ['FCU']
    } else if (activeMenus.value.name.includes('新风')) {
        typeCode = ['PAU']
    } else if (activeMenus.value.name.includes('排污井')) {
        typeCode = ['JKS']
    }
    let onCount = 0,
        offCount = 0,
        faultCount = 0,
        allCount = 0;
    let areaId = null;
    api.getDeviceTypeStatus({
        projectId: getCookie("gh_projectId"),
        typeCode: typeCode,
        areaId: areaId
    }).then(res => {
        res.data.forEach(d => {
            //空调系统和全热新风
            if (d.identifier == "runStatus" || d.identifier == "sts") {
                allCount++;
                if (d.value == '1') {
                    onCount++;

                } else if (d.value == '0') {
                    offCount++;
                }
            }

        })
        on.value = onCount;
        off.value = offCount;
        all.value = allCount;
        fault.value = faultCount;
    });
}


getRunConfigPage()
getRunManualPage();
getDeviceTypeStatus();




</script>

<style lang="scss" scoped>
.right {
  // 添加 flex 布局使 flex 比例生效
  display: flex;
  flex-direction: column;
  gap: 16px;

  // 确保 .item 元素能正确使用 flex 比例
  .item {
    display: flex;
    flex-direction: column;
    min-height: 0; // 防止 flex 子元素溢出
  }

  :deep(.sub_title) {
    .item {
      // 覆盖全局 sub-title 样式，使用设备操作风格
      background: linear-gradient(90deg, rgba(45, 85, 135, 0.3) 0%, transparent 100%);
      border-left: 3px solid #2d87e6;
      padding: 10px 12px;
      border-radius: 4px;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 12px;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        background: url('@/assets/images/common/head.png') no-repeat center;
        background-size: contain;
        filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.4));
      }

      &::after {
        display: none;
      }

      .msg {
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: 500;
        color: #e6f4ff;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        margin-left: 28px;
        background: none;
        background-clip: unset;
        -webkit-background-clip: unset;
        letter-spacing: normal;
      }
    }
  }

  .item-body {
    height: calc(100% - 48px);
    padding: 16px;
    padding-top: 0px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1; // 让内容区域占满剩余空间

    &.order {
      padding: 10px;
      border-radius: 12px;
      position: relative;
      overflow: hidden;

      // 添加背景装饰
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle at 30% 70%,
          rgba(61, 233, 250, 0.03) 0%,
          transparent 50%);
        animation: ripple 8s ease-in-out infinite;
      }
    }
  }
}</style>
