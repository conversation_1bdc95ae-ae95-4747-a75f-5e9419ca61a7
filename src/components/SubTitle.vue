<template>
    <div class="sub_title">
        <div class="item">
            <span class="msg">{{ title }}</span>
        </div>
        <slot />
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: {
        title: String,
    },
    setup() {
        const state = reactive({
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
.sub_title {
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: space-between;

    .item {
        width: 393px;
        display: flex;
        align-items: flex-end;
        height: 40px;
        background: url("@/assets/images/menu/title.png") no-repeat;
        background-size: 100% 100%;

        .msg {
            font-size: 16px;
            font-family: "DOUYU";
            font-weight: normal;
            color: transparent;
            margin-left: 30px;
            margin-bottom: 10px;
            background: linear-gradient(0deg, #3797eb, #fefeff 85%, #fff);
            -webkit-background-clip: text;
        }
    }
}
</style>
