<template>
<div class="global-table run-table">
    <div class="table-container">
        <!-- 搜索表单 -->
        <el-form :inline="true" class="search-form" size="small">
            <el-form-item label="时间">
                <el-date-picker
                    v-model="date"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button class="search-btn enhanced-search-btn" type="primary" @click="getRunManualLogList">
                    <i class="iconfont icon-search search-icon"></i>
                    <span class="search-text">查询</span>
                </el-button>
            </el-form-item>
        </el-form>

        <!-- 表格主体 -->
        <el-table
            :data="list"
            height="calc(100% - 140px)"
            fit
            table-layout="auto">
            <template #empty>
                <no-data />
            </template>

            <!-- 时间列 -->
            <el-table-column prop="logTime" label="时间" align="center" width="180">
                <template #default="scope">
                    <div class="table-time-column">
                        <i class="iconfont icon-time time-icon"></i>
                        <span class="time-text">{{ scope.row.logTime }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 设备列 -->
            <el-table-column prop="deviceName" label="设备" align="center" min-width="150">
                <template #default="scope">
                    <div class="table-device-column">
                        <i class="iconfont icon-device device-icon"></i>
                        <span class="device-name" :title="scope.row.deviceName">{{ scope.row.deviceName }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 指标列 -->
            <el-table-column prop="standardName" label="指标" align="center" min-width="150">
                <template #default="scope">
                    <div class="table-standard-column">
                        <i class="iconfont icon-standard standard-icon"></i>
                        <span class="standard-name" :title="scope.row.standardName">{{ scope.row.standardName }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作内容列 -->
            <el-table-column label="操作内容" align="left" min-width="200">
                <template #default="scope">
                    <div class="table-operation-column">
                        <i class="iconfont icon-operation operation-icon"></i>
                        <span class="operation-text" :title="`数值从${scope.row.oldValue}改变为${scope.row.newValue}`">
                            数值从{{ scope.row.oldValue }}改变为{{ scope.row.newValue }}
                        </span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作者列 -->
            <el-table-column prop="username" label="操作者" align="center" width="120">
                <template #default="scope">
                    <div class="table-user-column">
                        <i class="iconfont icon-user user-icon"></i>
                        <span class="user-name" :title="scope.row.username">{{ scope.row.username }}</span>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="table-pagination center">
            <el-pagination
                prev-icon="CaretLeft"
                next-icon="CaretRight"
                :page-size="size"
                :current-page="page"
                layout="total,prev, pager, next"
                @current-change="handleCurrentChange"
                :total="total">
            </el-pagination>
        </div>

    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    reactive,
    toRefs,
    onMounted,
    inject,
    computed,
    watch
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'


export default {

    props: ['deviceId'],
    components: {

    },
    setup(props) {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            userId: '',
            page: 1,
            size: 10,
            total: 0,
            status: '',
            list: [],
            sources: [],
            levels: [],
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getRunManualLogList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getRunManualLogList()
            }
        })
        const getRunManualLogList = () => {
            api.getRunManualLog({
                projectId: getCookie("gh_projectId"),
                bt: dayjs(state.date[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.date[1]).format("YYYY-MM-DD HH:mm:ss"),
                page: state.page,
                size: 10,
                deviceId: props.deviceId,
            }).then((res) => {
                state.total = res.total
                state.list = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getRunManualLogList()
        }
        const getSourceName = (data) => {
            let name = ''
            state.sources.forEach((d) => {
                if (d.tagValue == data.alarmSource) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const getLevelName = (data) => {
            let name = ''
            state.levels.forEach((d) => {
                if (d.tagValue == data.alarmSource) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }

        return {
            ...toRefs(state),
            getRunManualLogList,
            handleCurrentChange,
            getSourceName,
            getLevelName,
            projectId
        }
    },

}
</script>

<style lang="scss">
// 组件特定的样式调整
.run-table {
    height: calc(100% - 56px);
    .table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    // 确保表格占满剩余空间
    .el-table {
        flex: 1;
    }

    // 美化查询按钮
    .enhanced-search-btn {
        position: relative;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.9) !important;
        border-radius: 8px !important;
        padding: 10px 20px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #FFFFFF !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        box-shadow:
            0 4px 12px rgba(61, 233, 250, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
        backdrop-filter: blur(8px) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
        min-width: 100px !important;

        // 渐变光效
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        // 悬停效果
        &:hover {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%) !important;
            border-color: rgba(61, 233, 250, 1) !important;
            box-shadow:
                0 6px 20px rgba(61, 233, 250, 0.4),
                0 0 20px rgba(61, 233, 250, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
            transform: translateY(-2px) scale(1.02) !important;

            &::before {
                left: 100%;
            }

            .search-icon {
                transform: rotate(360deg) scale(1.1);
                color: #FFFFFF;
            }

            .search-text {
                letter-spacing: 0.5px;
            }
        }

        // 点击效果
        &:active {
            transform: translateY(0) scale(0.98) !important;
            box-shadow:
                0 2px 8px rgba(61, 233, 250, 0.4),
                inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }

        // 图标样式
        .search-icon {
            font-size: 16px !important;
            margin-right: 8px !important;
            transition: all 0.3s ease !important;
            color: rgba(255, 255, 255, 0.9);
        }

        // 文字样式
        .search-text {
            font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            letter-spacing: 0.2px;
        }

        // 禁用状态
        &:disabled {
            background: linear-gradient(135deg, rgba(136, 156, 195, 0.3) 0%, rgba(136, 156, 195, 0.2) 100%) !important;
            border-color: rgba(136, 156, 195, 0.4) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            box-shadow: none !important;
            transform: none !important;
            cursor: not-allowed !important;

            &::before {
                display: none;
            }

            .search-icon {
                color: rgba(255, 255, 255, 0.5);
            }
        }

        // 聚焦状态
        &:focus {
            outline: none !important;
            box-shadow:
                0 4px 12px rgba(61, 233, 250, 0.3),
                0 0 0 3px rgba(61, 233, 250, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
        }
    }

    // 表格列图标样式
    .table-time-column,
    .table-device-column,
    .table-standard-column,
    .table-operation-column,
    .table-user-column {
        display: flex;
        align-items: center;
        gap: 8px;

        .time-icon,
        .device-icon,
        .standard-icon,
        .operation-icon,
        .user-icon {
            font-size: 14px;
            color: rgba(61, 233, 250, 0.8);
            flex-shrink: 0;
        }

        .time-text,
        .device-name,
        .standard-name,
        .operation-text,
        .user-name {
            color: #c7dfff;
            font-family: "Alibaba-PuHuiTi";
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    // 操作内容列左对齐特殊处理
    .table-operation-column {
        justify-content: flex-start;

        .operation-text {
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.4;
        }
    }
}
</style>
