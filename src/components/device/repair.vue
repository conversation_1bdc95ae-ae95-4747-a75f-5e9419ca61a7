<template>
<div class="global-table repair-table">
    <div class="table-container">
        <!-- 搜索表单 -->
        <el-form :inline="true" class="search-form" size="small">
            <el-form-item label="处理时间">
                <el-date-picker
                    v-model="date"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button class="search-btn enhanced-search-btn" type="primary" @click="getProcessLogList">
                    <i class="iconfont icon-search search-icon"></i>
                    <span class="search-text">查询</span>
                </el-button>
            </el-form-item>
        </el-form>

        <!-- 表格主体 -->
        <el-table
            :data="list"
            height="calc(100% - 140px)"
            fit
            table-layout="auto">
            <template #empty>
                <no-data />
            </template>

            <!-- 维修设备列 -->
            <el-table-column prop="deviceName" label="维修设备" align="center" min-width="150">
                <template #default="scope">
                    <div class="table-device-column">
                        <i class="iconfont icon-device device-icon"></i>
                        <span class="device-name" :title="scope.row.deviceName">{{ scope.row.deviceName }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 维修时间列 -->
            <el-table-column prop="createTime" label="维修时间" align="center" width="180">
                <template #default="scope">
                    <div class="table-time-column">
                        <i class="iconfont icon-time time-icon"></i>
                        <span class="time-text">{{ scope.row.createTime }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 维修人员列 -->
            <el-table-column prop="creatorName" label="维修人员" align="center" width="120">
                <template #default="scope">
                    <div class="table-user-column">
                        <i class="iconfont icon-user user-icon"></i>
                        <span class="user-name" :title="scope.row.creatorName">{{ scope.row.creatorName }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 维修内容列 -->
            <el-table-column prop="description" label="维修内容" align="left" min-width="200">
                <template #default="scope">
                    <div class="table-content-column">
                        <i class="iconfont icon-content content-icon"></i>
                        <span class="content-text" :title="scope.row.description">{{ scope.row.description }}</span>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="table-pagination center">
            <el-pagination
                prev-icon="CaretLeft"
                next-icon="CaretRight"
                :page-size="size"
                :current-page="page"
                layout="total,prev, pager, next"
                @current-change="handleCurrentChange"
                :total="total">
            </el-pagination>
        </div>

    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    reactive,
    toRefs,
    onMounted,
    inject,
    computed,
    watch
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'



export default {

    props: ['deviceId'],
    setup(props) {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            list: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            date: [],
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getProcessLogList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getProcessLogList()
        })
        const getProcessLogList = () => {
            api.getProcessLog({
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                type: 1,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogList()
        }
        return {
            ...toRefs(state),
            getProcessLogList,
            handleCurrentChange,
            projectId
        }
    }
}
</script>

<style lang="scss">
// 组件特定的样式调整
.repair-table {
    height: calc(100% - 56px);
    .table-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    // 确保表格占满剩余空间
    .el-table {
        flex: 1;
    }

    // 美化查询按钮
    .enhanced-search-btn {
        position: relative;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.9) !important;
        border-radius: 8px !important;
        padding: 10px 20px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #FFFFFF !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        box-shadow:
            0 4px 12px rgba(61, 233, 250, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
        backdrop-filter: blur(8px) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
        min-width: 100px !important;

        // 渐变光效
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        // 悬停效果
        &:hover {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%) !important;
            border-color: rgba(61, 233, 250, 1) !important;
            box-shadow:
                0 6px 20px rgba(61, 233, 250, 0.4),
                0 0 20px rgba(61, 233, 250, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
            transform: translateY(-2px) scale(1.02) !important;

            &::before {
                left: 100%;
            }

            .search-icon {
                transform: rotate(360deg) scale(1.1);
                color: #FFFFFF;
            }

            .search-text {
                letter-spacing: 0.5px;
            }
        }

        // 点击效果
        &:active {
            transform: translateY(0) scale(0.98) !important;
            box-shadow:
                0 2px 8px rgba(61, 233, 250, 0.4),
                inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }

        // 图标样式
        .search-icon {
            font-size: 16px !important;
            margin-right: 8px !important;
            transition: all 0.3s ease !important;
            color: rgba(255, 255, 255, 0.9);
        }

        // 文字样式
        .search-text {
            font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            letter-spacing: 0.2px;
        }

        // 禁用状态
        &:disabled {
            background: linear-gradient(135deg, rgba(136, 156, 195, 0.3) 0%, rgba(136, 156, 195, 0.2) 100%) !important;
            border-color: rgba(136, 156, 195, 0.4) !important;
            color: rgba(255, 255, 255, 0.5) !important;
            box-shadow: none !important;
            transform: none !important;
            cursor: not-allowed !important;

            &::before {
                display: none;
            }

            .search-icon {
                color: rgba(255, 255, 255, 0.5);
            }
        }

        // 聚焦状态
        &:focus {
            outline: none !important;
            box-shadow:
                0 4px 12px rgba(61, 233, 250, 0.3),
                0 0 0 3px rgba(61, 233, 250, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
        }
    }

    // 表格列图标样式
    .table-device-column,
    .table-time-column,
    .table-user-column,
    .table-content-column {
        display: flex;
        align-items: center;
        gap: 8px;

        .device-icon,
        .time-icon,
        .user-icon,
        .content-icon {
            font-size: 14px;
            color: rgba(61, 233, 250, 0.8);
            flex-shrink: 0;
        }

        .device-name,
        .time-text,
        .user-name,
        .content-text {
            color: #c7dfff;
            font-family: "Alibaba-PuHuiTi";
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    // 内容列左对齐特殊处理
    .table-content-column {
        justify-content: flex-start;

        .content-text {
            white-space: normal;
            word-wrap: break-word;
            line-height: 1.4;
        }
    }
}
</style>
