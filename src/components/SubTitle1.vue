<template>
    <div class="sub_title">
        <div class="item">
            <span class="msg">{{ title }}</span>
        </div>
        <slot />
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: {
        title: String,
    },
    setup() {
        const state = reactive({
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
// 全局标题组件样式 - 统一的毛玻璃风格
.sub_title {
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .item {
        width: 100%;
        display: flex;
        align-items: center;
        height: 40px;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 50%, transparent 100%);
        backdrop-filter: blur(8px);
        border-left: 3px solid #3de9fa;
        border-radius: 8px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
            opacity: 0.5;
        }

        &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, #3de9fa 0%, transparent 100%);
        }

        &:hover {
            border-color: rgba(61, 233, 250, 0.6);
            box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .msg {
            font-size: 18px;
            font-family: "DOUYU" !important;
            font-weight: 600;
            color: transparent;
            margin-left: 20px;
            background: linear-gradient(135deg, #3de9fa 0%, #ffffff 50%, #1196FC 100%);
            -webkit-background-clip: text;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
    }
}
</style>
